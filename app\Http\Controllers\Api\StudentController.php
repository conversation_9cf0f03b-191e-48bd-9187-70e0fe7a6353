<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Student;

class StudentController extends Controller
{
    public function profile(Request $request)
    {
        $student = $request->user();
        return response()->json([
            'student' => [
                'id' => $student->id,
                'email' => $student->email,
                'english_name' => $student->english_name,
                'arabic_name' => $student->arabic_name,
                'profile_photo' => $student->profile_photo,
                'gender' => $student->gender,
                'age' => $student->age,
                'dob' => $student->dob->format('Y-m-d'),
                'parent_number' => $student->parent_number,
                'weight' => $student->weight,
                'height' => $student->height,
                'school_name' => $student->school_name,
                'level_of_player' => $student->level_of_player,
                'position' => $student->position,
                'category' => $student->category,
                'balance' => $student->balance,
                'instagram' => $student->instagram,
                'facebook' => $student->facebook,
                'registration_date' => $student->registration_date->format('Y-m-d'),
            ]
        ]);
    }

    public function classes(Request $request)
    {
        $student = $request->user();
        $classes = $student->classes()
            ->with('instructor') // Using the correct relationship from ClassModel
            ->whereHas('instructor')
            ->where('is_active', true)
            ->get()
            ->map(function($class) {
                $dayNames = [
                    1 => 'Monday',
                    2 => 'Tuesday',
                    3 => 'Wednesday',
                    4 => 'Thursday',
                    5 => 'Friday',
                    6 => 'Saturday',
                    7 => 'Sunday'
                ];

                return [
                    'id' => $class->id,
                    'name' => $class->class_name,
                    'instructor_name' => $class->instructor?->name, // Using the instructor relationship
                    'schedule' => sprintf('%s %s-%s',
                        $dayNames[$class->day_of_week] ?? '',
                        $class->start_time?->format('H:i'),
                        $class->end_time?->format('H:i')
                    ),
                    'description' => $class->description,
                    'price_per_month' => $class->price_per_month,
                ];
            });

        return response()->json(['classes' => $classes]);
    }

    public function attendances(Request $request)
    {
        $student = $request->user();
        $attendances = $student->attendances()
            ->with(['sessionn']) // Include session relationship
            ->join('sessionns', 'attendances.sessionn_id', '=', 'sessionns.id')
            ->orderBy('sessionns.sessionn_date', 'desc')
            ->take(30)
            ->get()
            ->map(function($attendance) {
                return [
                    'id' => $attendance->id,
                    'class_name' => $attendance->sessionn->class->class_name,
                    'date' => $attendance->sessionn->sessionn_date->format('Y-m-d'),
                    'attended' => $attendance->attended,
                ];
            });

        return response()->json(['attendances' => $attendances]);
    }

    public function payments(Request $request)
    {
        $student = $request->user();
        $payments = $student->payments()
            ->with('classModel')  // Changed from 'class' to 'classModel'
            ->orderBy('payment_date', 'desc')
            ->take(20)
            ->get()
            ->map(function($payment) {
                return [
                    'id' => $payment->id,
                    'amount' => $payment->amount,
                    'class_name' => $payment->classModel?->class_name ?? 'N/A',  // Changed from class to classModel
                    'payment_date' => $payment->payment_date->format('Y-m-d'),
                    'payment_method' => $payment->payment_method,
                    'status' => $payment->status,
                ];
            });

        return response()->json(['payments' => $payments]);
    }
}
