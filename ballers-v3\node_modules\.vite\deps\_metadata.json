{"hash": "512d3488", "configHash": "dd38cce4", "lockfileHash": "aeb7707d", "browserHash": "b60a84de", "optimized": {"lucide-react": {"src": "../../lucide-react/dist/esm/icons/index.js", "file": "lucide-react.js", "fileHash": "9a932791", "needsInterop": false}, "react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "033133a5", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "818e9fc0", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "52f0dae1", "needsInterop": true}, "@fortawesome/free-solid-svg-icons": {"src": "../../@fortawesome/free-solid-svg-icons/index.mjs", "file": "@fortawesome_free-solid-svg-icons.js", "fileHash": "e3f27608", "needsInterop": false}, "@fortawesome/react-fontawesome": {"src": "../../@fortawesome/react-fontawesome/index.es.js", "file": "@fortawesome_react-fontawesome.js", "fileHash": "e9dd0fc8", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "0669ccfe", "needsInterop": false}, "i18next": {"src": "../../i18next/dist/esm/i18next.js", "file": "i18next.js", "fileHash": "dd553573", "needsInterop": false}, "i18next-browser-languagedetector": {"src": "../../i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js", "file": "i18next-browser-languagedetector.js", "fileHash": "956e5bed", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "10883d4f", "needsInterop": true}, "react-hot-toast": {"src": "../../react-hot-toast/dist/index.mjs", "file": "react-hot-toast.js", "fileHash": "e0290163", "needsInterop": false}, "react-i18next": {"src": "../../react-i18next/dist/es/index.js", "file": "react-i18next.js", "fileHash": "cf75a53c", "needsInterop": false}, "react-icons/fa": {"src": "../../react-icons/fa/index.mjs", "file": "react-icons_fa.js", "fileHash": "31ed4a8e", "needsInterop": false}, "react-icons/io5": {"src": "../../react-icons/io5/index.mjs", "file": "react-icons_io5.js", "fileHash": "955a52d6", "needsInterop": false}, "react-image-gallery": {"src": "../../react-image-gallery/build/image-gallery.es.js", "file": "react-image-gallery.js", "fileHash": "8f7fe49f", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "c43fcbe5", "needsInterop": false}}, "chunks": {"chunk-OICGFGSZ": {"file": "chunk-OICGFGSZ.js"}, "chunk-6BKLQ22S": {"file": "chunk-6BKLQ22S.js"}, "chunk-DRWLMN53": {"file": "chunk-DRWLMN53.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}