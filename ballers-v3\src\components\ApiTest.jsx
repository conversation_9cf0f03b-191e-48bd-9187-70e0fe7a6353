import React, { useState } from 'react';
import axios from 'axios';

const ApiTest = () => {
  const [testResult, setTestResult] = useState('');
  const [loading, setLoading] = useState(false);

  const testApiConnection = async () => {
    setLoading(true);
    setTestResult('Testing API connection...');
    
    try {
      // Test basic connection
      const response = await axios.get('http://localhost:8000/api/test', {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        }
      });
      
      setTestResult(`✅ API Connection successful: ${JSON.stringify(response.data)}`);
    } catch (error) {
      setTestResult(`❌ API Connection failed: ${error.message}\nStatus: ${error.response?.status}\nData: ${JSON.stringify(error.response?.data)}`);
    } finally {
      setLoading(false);
    }
  };

  const testLogin = async () => {
    setLoading(true);
    setTestResult('Testing login endpoint...');
    
    try {
      const response = await axios.post('http://localhost:8000/api/login', {
        email: '<EMAIL>',
        password: 'password123'
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        }
      });
      
      setTestResult(`✅ Login test successful: ${JSON.stringify(response.data)}`);
    } catch (error) {
      setTestResult(`❌ Login test failed: ${error.message}\nStatus: ${error.response?.status}\nData: ${JSON.stringify(error.response?.data, null, 2)}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'monospace' }}>
      <h2>API Connection Test</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={testApiConnection} 
          disabled={loading}
          style={{ marginRight: '10px', padding: '10px' }}
        >
          Test API Connection
        </button>
        
        <button 
          onClick={testLogin} 
          disabled={loading}
          style={{ padding: '10px' }}
        >
          Test Login Endpoint
        </button>
      </div>
      
      <div style={{ 
        background: '#f5f5f5', 
        padding: '15px', 
        borderRadius: '5px',
        whiteSpace: 'pre-wrap',
        minHeight: '100px'
      }}>
        {loading ? 'Loading...' : testResult || 'Click a button to test the API'}
      </div>
      
      <div style={{ marginTop: '20px', fontSize: '14px', color: '#666' }}>
        <h3>Debug Info:</h3>
        <p>Frontend URL: {window.location.origin}</p>
        <p>API Base URL: http://localhost:8000/api</p>
        <p>Current Time: {new Date().toISOString()}</p>
      </div>
    </div>
  );
};

export default ApiTest;
