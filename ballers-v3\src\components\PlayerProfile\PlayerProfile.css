/* Profile Cover */
.profile-cover {
  position: relative;
  width: 100%;
  height: 300px;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.profile-cover__image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0.8;
}

/* Profile Header */
.profile-header {
  position: relative;
  padding: 0 2rem;
  margin-bottom: 2rem;
}

.profile-header__main {
  display: flex;
  align-items: end;
  gap: 1.5rem;
  margin-top: -4rem;
  position: relative;
  z-index: 10;
}

/* Profile Avatar */
.profile-avatar {
  position: relative;
  flex-shrink: 0;
}

.profile-avatar__image {
  width: 8rem;
  height: 8rem;
  border-radius: 50%;
  border: 4px solid white;
  object-fit: cover;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.profile-avatar__change-btn {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background: white;
  border: 2px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.profile-avatar__change-btn:hover {
  background: #f3f4f6;
  transform: scale(1.05);
}

/* Profile Info */
.profile-info {
  flex: 1;
  min-width: 0;
}

.profile-info__name {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
  line-height: 1.2;
}

.profile-info__meta {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-top: 0.5rem;
  color: #6b7280;
  font-size: 0.875rem;
  flex-wrap: wrap;
}

.profile-info__meta svg {
  color: #ef6c00;
}

/* Cards */
.card {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card__header {
  padding: 1.5rem 1.5rem 0;
}

.card__title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.card__subtitle {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0.25rem 0 0;
}

.card__content {
  padding: 1.5rem;
}

.card__footer {
  padding: 0 1.5rem 1.5rem;
}

/* Progress Bar */
.card__progress {
  width: 100%;
  height: 0.5rem;
  background: #e5e7eb;
  border-radius: 0.25rem;
  overflow: hidden;
  margin: 1rem 0;
}

.card__progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #10b981, #059669);
  border-radius: 0.25rem;
  transition: width 0.3s ease;
}

/* Stats */
.card__stat {
  text-align: center;
}

.card__stat-value {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
}

.card__stat-label {
  display: block;
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

/* Payment Status */
.card__payment {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  border-bottom: 1px solid #e5e7eb;
}

.card__payment:last-child {
  border-bottom: none;
}

.card__payment-status {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.card__payment-status--paid {
  background: #d1fae5;
  color: #065f46;
}

.card__payment-status--pending {
  background: #fef3c7;
  color: #92400e;
}

.card__payment-status--overdue {
  background: #fee2e2;
  color: #991b1b;
}

/* Attendance Item */
.attendance-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #e5e7eb;
}

.attendance-item:last-child {
  border-bottom: none;
}

.attendance-item__info h4 {
  font-weight: 500;
  color: #1f2937;
  margin: 0;
}

.attendance-item__info p {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0.25rem 0 0;
}

.attendance-item__status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.attendance-item__status--present {
  color: #059669;
}

.attendance-item__status--absent {
  color: #dc2626;
}

/* Class Item */
.class-item {
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
  transition: all 0.2s ease;
}

.class-item:hover {
  border-color: #ef6c00;
  box-shadow: 0 2px 8px rgba(239, 108, 0, 0.1);
}

.class-item__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.class-item__title {
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.class-item__price {
  font-weight: 600;
  color: #ef6c00;
  font-size: 0.875rem;
}

.class-item__instructor {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0.25rem 0;
}

.class-item__schedule {
  font-size: 0.875rem;
  color: #374151;
  font-weight: 500;
}

.class-item__description {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.5rem;
  line-height: 1.4;
}

/* Tab Navigation */
.tab-navigation {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.tab-navigation::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.tab-navigation .flex {
  border-bottom: 1px solid #e5e7eb;
  min-width: max-content;
  display: flex;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  font-weight: 500;
  transition: all 0.2s ease;
  white-space: nowrap;
  border-bottom: 2px solid transparent;
  color: #6b7280;
  cursor: pointer;
  background: none;
  border: none;
  font-size: 0.875rem;
  min-height: 44px; /* Touch-friendly minimum height */
  text-decoration: none;
}

.tab-button:hover {
  color: #374151;
}

.tab-button.active {
  border-bottom-color: #ef6c00;
  color: #ef6c00;
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-header {
    padding: 0 1rem;
  }

  .profile-header__main {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 1rem;
  }

  .profile-info__name {
    font-size: 1.5rem;
  }

  .profile-info__meta {
    justify-content: center;
  }

  .profile-avatar__image {
    width: 6rem;
    height: 6rem;
  }

  /* Mobile tab navigation */
  .tab-navigation {
    margin: 0 -1rem 1.5rem -1rem;
    border-radius: 0;
  }

  .tab-navigation .flex {
    min-width: 100%;
  }

  .tab-button {
    padding: 0.75rem 0.5rem;
    font-size: 0.8rem;
    min-width: auto;
    flex: 1;
    justify-content: center;
  }

  .tab-button svg {
    width: 16px;
    height: 16px;
  }

  /* Mobile container adjustments */
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Mobile grid adjustments */
  .grid {
    gap: 1rem;
  }

  /* Mobile card adjustments */
  .card {
    margin-bottom: 1rem;
  }

  .card__content {
    padding: 1rem;
  }

  .card__header {
    padding: 1rem 1rem 0;
  }

  .card__footer {
    padding: 0 1rem 1rem;
  }
}

/* Very small screens */
@media (max-width: 480px) {
  .tab-button span {
    display: none;
  }

  .tab-button {
    padding: 0.75rem 0.5rem;
    justify-content: center;
    flex: 1;
  }

  .profile-info__name {
    font-size: 1.25rem;
  }

  .profile-info__meta {
    font-size: 0.75rem;
    gap: 0.5rem;
  }

  .card__title {
    font-size: 1rem;
  }

  .card__subtitle {
    font-size: 0.8rem;
  }
}
