import React, { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';
import { studentAPI } from '../../services/api';
import { calculateAttendanceStats, formatCurrency, formatDate } from '../../utils/helpers';
import toast from 'react-hot-toast';
import {
  User,
  Calendar,
  TrendingUp,
  DollarSign,
  BookOpen,
  Clock,
  CheckCircle,
  XCircle,
  LogOut,
  Camera,
  Mail,
  Phone,
  MapPin,
  Award,
  Target,
  Users
} from 'lucide-react';

import Navbar from '../NavBar/Navbar';
import Footer from '../Footer/Footer';
import './PlayerProfile.css';

const PlayerProfile = () => {
  const { user, logout } = useAuth();
  const [profileData, setProfileData] = useState(null);
  const [classes, setClasses] = useState([]);
  const [attendances, setAttendances] = useState([]);
  const [payments, setPayments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    fetchAllData();
  }, []);

  const fetchAllData = async () => {
    try {
      setLoading(true);
      const [profileRes, classesRes, attendancesRes, paymentsRes] = await Promise.all([
        studentAPI.getProfile(),
        studentAPI.getClasses(),
        studentAPI.getAttendances(),
        studentAPI.getPayments()
      ]);

      setProfileData(profileRes.student);
      setClasses(classesRes.classes || []);
      setAttendances(attendancesRes.attendances || []);
      setPayments(paymentsRes.payments || []);
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Failed to load profile data');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      toast.success('Logged out successfully');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };



  const getTotalDue = () => {
    return payments
      .filter(p => p.status === 'pending')
      .reduce((sum, p) => sum + parseFloat(p.amount), 0);
  };

  if (loading) {
    return (
      <div>
        <Navbar />
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading profile...</p>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  if (!profileData) {
    return (
      <div>
        <Navbar />
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <p className="text-red-600">Failed to load profile data</p>
            <button
              onClick={fetchAllData}
              className="mt-4 px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600"
            >
              Retry
            </button>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  const attendanceStats = calculateAttendanceStats(attendances);
  const totalDue = getTotalDue();

  return (
    <div>
      <Navbar />
      <div className="min-h-screen bg-gradient-to-b from-slate-50 to-white">
        {/* Profile Header */}
        <div className="profile-cover">
          <img
            src="https://images.pexels.com/photos/1752757/pexels-photo-1752757.jpeg"
            alt="Cover"
            className="profile-cover__image"
          />
          <button
            onClick={handleLogout}
            className="absolute top-4 right-4 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
          >
            <LogOut size={18} />
            Logout
          </button>
        </div>

        <div className="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="profile-header">
            <div className="profile-header__main">
              <div className="profile-avatar">
                <img
                  src={profileData.profile_photo || "https://images.pexels.com/photos/2834917/pexels-photo-2834917.jpeg"}
                  alt={profileData.english_name}
                  className="profile-avatar__image"
                />
                <button className="profile-avatar__change-btn">
                  <Camera size={14} />
                </button>
              </div>
              <div className="profile-info">
                <div className="flex items-center gap-3 flex-wrap">
                  <h1 className="profile-info__name">{profileData.english_name}</h1>
                  {profileData.arabic_name && (
                    <span className="text-lg text-gray-600">({profileData.arabic_name})</span>
                  )}
                </div>
                <div className="profile-info__meta">
                  <Target size={14} className="text-primary-500" />
                  <span>{profileData.position}</span>
                  <Award size={14} className="text-primary-500" />
                  <span>{profileData.level_of_player}</span>
                  <Calendar size={14} className="text-primary-500" />
                  <span>Joined {formatDate(profileData.registration_date)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Navigation Tabs */}
          <div className="tab-navigation">
            <div className="flex">
              {[
                { id: 'overview', label: 'Overview', icon: User },
                { id: 'classes', label: 'Classes', icon: BookOpen },
                { id: 'attendance', label: 'Attendance', icon: Clock },
                { id: 'payments', label: 'Payments', icon: DollarSign }
              ].map(tab => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}
                >
                  <tab.icon size={18} />
                  <span>{tab.label}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Tab Content */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {activeTab === 'overview' && (
              <>
                {/* Personal Information */}
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <User size={20} className="text-orange-500" />
                    Personal Information
                  </h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Age</span>
                      <span className="font-medium">{profileData.age}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Height</span>
                      <span className="font-medium">{profileData.height}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Weight</span>
                      <span className="font-medium">{profileData.weight}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Gender</span>
                      <span className="font-medium">{profileData.gender}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Category</span>
                      <span className="font-medium">{profileData.category}</span>
                    </div>
                    {profileData.school_name && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">School</span>
                        <span className="font-medium">{profileData.school_name}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Contact Information */}
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <Phone size={20} className="text-orange-500" />
                    Contact Information
                  </h3>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <Mail size={16} className="text-gray-400" />
                      <span className="text-sm">{profileData.email}</span>
                    </div>
                    {profileData.parent_number && (
                      <div className="flex items-center gap-3">
                        <Phone size={16} className="text-gray-400" />
                        <span className="text-sm">{profileData.parent_number}</span>
                      </div>
                    )}
                    {profileData.instagram && (
                      <div className="flex items-center gap-3">
                        <Users size={16} className="text-gray-400" />
                        <span className="text-sm">@{profileData.instagram}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Quick Stats */}
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <TrendingUp size={20} className="text-orange-500" />
                    Quick Stats
                  </h3>
                  <div className="space-y-4">
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">{attendanceStats.rate}%</div>
                      <div className="text-sm text-gray-600">Attendance Rate</div>
                    </div>
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{classes.length}</div>
                      <div className="text-sm text-gray-600">Enrolled Classes</div>
                    </div>
                    <div className="text-center p-4 bg-orange-50 rounded-lg">
                      <div className="text-2xl font-bold text-orange-600">{formatCurrency(profileData.balance)}</div>
                      <div className="text-sm text-gray-600">Account Balance</div>
                    </div>
                  </div>
                </div>
              </>
            )}

            {activeTab === 'classes' && (
              <div className="lg:col-span-3">
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <BookOpen size={20} className="text-orange-500" />
                    Enrolled Classes ({classes.length})
                  </h3>
                  {classes.length > 0 ? (
                    <div className="space-y-4">
                      {classes.map((classItem, index) => (
                        <div key={index} className="class-item">
                          <div className="class-item__header">
                            <h4 className="class-item__title">{classItem.name}</h4>
                            <span className="class-item__price">{formatCurrency(classItem.price_per_month)}/month</span>
                          </div>
                          {classItem.instructor_name && (
                            <p className="class-item__instructor">Instructor: {classItem.instructor_name}</p>
                          )}
                          <p className="class-item__schedule">{classItem.schedule}</p>
                          {classItem.description && (
                            <p className="class-item__description">{classItem.description}</p>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <BookOpen size={48} className="mx-auto mb-4 text-gray-300" />
                      <p>No classes enrolled yet</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {activeTab === 'attendance' && (
              <div className="lg:col-span-3">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Attendance Stats */}
                  <div className="bg-white rounded-lg shadow-sm p-6">
                    <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                      <TrendingUp size={20} className="text-orange-500" />
                      Attendance Statistics
                    </h3>
                    <div className="space-y-4">
                      <div className="text-center p-4 bg-green-50 rounded-lg">
                        <div className="text-3xl font-bold text-green-600">{attendanceStats.rate}%</div>
                        <div className="text-sm text-gray-600">Attendance Rate</div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="text-center p-3 bg-blue-50 rounded-lg">
                          <div className="text-xl font-bold text-blue-600">{attendanceStats.attended}</div>
                          <div className="text-xs text-gray-600">Attended</div>
                        </div>
                        <div className="text-center p-3 bg-red-50 rounded-lg">
                          <div className="text-xl font-bold text-red-600">{attendanceStats.missed}</div>
                          <div className="text-xs text-gray-600">Missed</div>
                        </div>
                      </div>
                      <div className="card__progress">
                        <div
                          className="card__progress-bar"
                          style={{ width: `${attendanceStats.rate}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>

                  {/* Attendance Records */}
                  <div className="lg:col-span-2 bg-white rounded-lg shadow-sm p-6">
                    <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                      <Clock size={20} className="text-orange-500" />
                      Recent Attendance ({attendances.length} records)
                    </h3>
                    {attendances.length > 0 ? (
                      <div className="space-y-3 max-h-96 overflow-y-auto">
                        {attendances.map((attendance, index) => (
                          <div key={index} className="attendance-item">
                            <div className="attendance-item__info">
                              <h4>{attendance.class_name}</h4>
                              <p>{formatDate(attendance.date)}</p>
                            </div>
                            <div className={`attendance-item__status ${
                              attendance.attended
                                ? 'attendance-item__status--present'
                                : 'attendance-item__status--absent'
                            }`}>
                              {attendance.attended ? (
                                <>
                                  <CheckCircle size={16} />
                                  Present
                                </>
                              ) : (
                                <>
                                  <XCircle size={16} />
                                  Absent
                                </>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-gray-500">
                        <Clock size={48} className="mx-auto mb-4 text-gray-300" />
                        <p>No attendance records found</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'payments' && (
              <div className="lg:col-span-3">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Payment Summary */}
                  <div className="bg-white rounded-lg shadow-sm p-6">
                    <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                      <DollarSign size={20} className="text-orange-500" />
                      Payment Summary
                    </h3>
                    <div className="space-y-4">
                      <div className="text-center p-4 bg-orange-50 rounded-lg">
                        <div className="text-2xl font-bold text-orange-600">{formatCurrency(profileData.balance)}</div>
                        <div className="text-sm text-gray-600">Account Balance</div>
                      </div>
                      <div className="text-center p-4 bg-red-50 rounded-lg">
                        <div className="text-2xl font-bold text-red-600">{formatCurrency(totalDue)}</div>
                        <div className="text-sm text-gray-600">Total Due</div>
                      </div>
                      {totalDue > 0 && (
                        <button className="w-full bg-orange-500 hover:bg-orange-600 text-white py-2 px-4 rounded-lg font-medium transition-colors">
                          Make Payment
                        </button>
                      )}
                    </div>
                  </div>

                  {/* Payment History */}
                  <div className="lg:col-span-2 bg-white rounded-lg shadow-sm p-6">
                    <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                      <Calendar size={20} className="text-orange-500" />
                      Payment History ({payments.length} records)
                    </h3>
                    {payments.length > 0 ? (
                      <div className="space-y-3 max-h-96 overflow-y-auto">
                        {payments.map((payment, index) => (
                          <div key={index} className="card__payment">
                            <div>
                              <h4 className="font-medium">{payment.class_name || 'Academy Fee'}</h4>
                              <p className="text-sm text-gray-600">
                                {formatDate(payment.payment_date)}
                                {payment.payment_method && ` • ${payment.payment_method}`}
                              </p>
                            </div>
                            <div className="flex items-center gap-3">
                              <span className="font-medium">{formatCurrency(payment.amount)}</span>
                              <span className={`card__payment-status card__payment-status--${payment.status}`}>
                                {payment.status}
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-gray-500">
                        <DollarSign size={48} className="mx-auto mb-4 text-gray-300" />
                        <p>No payment records found</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default PlayerProfile;
