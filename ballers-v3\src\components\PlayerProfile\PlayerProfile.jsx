import React, { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';
import { studentAPI } from '../../services/api';
import { calculateAttendanceStats, formatCurrency, formatDate } from '../../utils/helpers';
import toast from 'react-hot-toast';
import {
  User,
  Calendar,
  TrendingUp,
  DollarSign,
  BookOpen,
  Clock,
  CheckCircle,
  XCircle,
  LogOut,
  Camera,
  Mail,
  Phone,
  MapPin,
  Award,
  Target,
  Users
} from 'lucide-react';

import Navbar from '../NavBar/Navbar';
import Footer from '../Footer/Footer';
import './PlayerProfile.css';

const PlayerProfile = () => {
  const { user, logout } = useAuth();
  const [profileData, setProfileData] = useState(null);
  const [classes, setClasses] = useState([]);
  const [attendances, setAttendances] = useState([]);
  const [payments, setPayments] = useState([]);
  const [loading, setLoading] = useState(true);


  useEffect(() => {
    fetchAllData();
  }, []);

  const fetchAllData = async () => {
    try {
      setLoading(true);
      const [profileRes, classesRes, attendancesRes, paymentsRes] = await Promise.all([
        studentAPI.getProfile(),
        studentAPI.getClasses(),
        studentAPI.getAttendances(),
        studentAPI.getPayments()
      ]);

      setProfileData(profileRes.student);
      setClasses(classesRes.classes || []);
      setAttendances(attendancesRes.attendances || []);
      setPayments(paymentsRes.payments || []);
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Failed to load profile data');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      toast.success('Logged out successfully');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };



  const getTotalDue = () => {
    return payments
      .filter(p => p.status === 'pending')
      .reduce((sum, p) => sum + parseFloat(p.amount), 0);
  };

  if (loading) {
    return (
      <div>
        <Navbar />
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading profile...</p>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  if (!profileData) {
    return (
      <div>
        <Navbar />
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <p className="text-red-600">Failed to load profile data</p>
            <button
              onClick={fetchAllData}
              className="mt-4 px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600"
            >
              Retry
            </button>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  const attendanceStats = calculateAttendanceStats(attendances);
  const totalDue = getTotalDue();

  return (
    <div>
      <Navbar />
      <div className="min-h-screen bg-gradient-to-b from-slate-50 to-white">
        {/* Profile Header */}
        <div className="profile-cover">
          <img
            src="https://images.pexels.com/photos/1752757/pexels-photo-1752757.jpeg"
            alt="Cover"
            className="profile-cover__image"
          />
          <button
            onClick={handleLogout}
            className="absolute top-4 right-4 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
          >
            <LogOut size={18} />
            Logout
          </button>
        </div>

        <div className="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="profile-header">
            <div className="profile-header__main">
              <div className="profile-avatar">
                <img
                  src={profileData.profile_photo || "https://images.pexels.com/photos/2834917/pexels-photo-2834917.jpeg"}
                  alt={profileData.english_name}
                  className="profile-avatar__image"
                />
                <button className="profile-avatar__change-btn">
                  <Camera size={14} />
                </button>
              </div>
              <div className="profile-info">
                <div className="flex items-center gap-3 flex-wrap">
                  <h1 className="profile-info__name">{profileData.english_name}</h1>
                  {profileData.arabic_name && (
                    <span className="text-lg text-gray-600">({profileData.arabic_name})</span>
                  )}
                </div>
                <div className="profile-info__meta">
                  <Target size={14} className="text-primary-500" />
                  <span>{profileData.position}</span>
                  <Award size={14} className="text-primary-500" />
                  <span>{profileData.level_of_player}</span>
                  <Calendar size={14} className="text-primary-500" />
                  <span>Joined {formatDate(profileData.registration_date)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Profile Content - Original Layout Style */}
          <div className="profile-content">

            {/* Main Grid Layout */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">

              {/* Personal Information Card */}
              <div className="card">
                <div className="card__header">
                  <h2 className="card__title">Personal Information</h2>
                </div>
                <div className="card__content">
                  <div className="personal-info">
                    <div className="personal-info__item">
                      <span className="personal-info__label">Age</span>
                      <span className="personal-info__value">{profileData.age}</span>
                    </div>
                    <div className="personal-info__item">
                      <span className="personal-info__label">Height</span>
                      <span className="personal-info__value">{profileData.height}</span>
                    </div>
                    <div className="personal-info__item">
                      <span className="personal-info__label">Weight</span>
                      <span className="personal-info__value">{profileData.weight}</span>
                    </div>
                    <div className="personal-info__item">
                      <span className="personal-info__label">Gender</span>
                      <span className="personal-info__value">{profileData.gender}</span>
                    </div>
                    <div className="personal-info__item">
                      <span className="personal-info__label">Level</span>
                      <span className="personal-info__value">{profileData.level_of_player}</span>
                    </div>
                    {profileData.school_name && (
                      <div className="personal-info__item">
                        <span className="personal-info__label">School</span>
                        <span className="personal-info__value">{profileData.school_name}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Attendance Card */}
              <div className="card">
                <div className="card__header">
                  <h2 className="card__title">Attendance</h2>
                  <p className="card__subtitle">Current season attendance record</p>
                </div>
                <div className="card__content">
                  <div className="card__progress">
                    <div className="card__progress-bar" style={{ width: `${attendanceStats.rate}%` }}></div>
                  </div>
                  <div className="flex justify-between mt-4">
                    <div className="card__stat">
                      <span className="card__stat-value">{attendanceStats.attended}</span>
                      <span className="card__stat-label">Attended</span>
                    </div>
                    <div className="card__stat">
                      <span className="card__stat-value">{attendanceStats.missed}</span>
                      <span className="card__stat-label">Missed</span>
                    </div>
                    <div className="card__stat">
                      <span className="card__stat-value">{attendanceStats.total}</span>
                      <span className="card__stat-label">Total</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Classes Card */}
              <div className="card">
                <div className="card__header">
                  <h2 className="card__title">Enrolled Classes</h2>
                </div>
                <div className="card__content">
                  <div className="space-y-3">
                    {classes.length > 0 ? (
                      classes.slice(0, 3).map((classItem, index) => (
                        <div key={index} className="class-item">
                          <h4 className="class-item__title">{classItem.name}</h4>
                          {classItem.instructor_name && (
                            <p className="class-item__instructor">Instructor: {classItem.instructor_name}</p>
                          )}
                          <p className="class-item__schedule">{classItem.schedule}</p>
                        </div>
                      ))
                    ) : (
                      <p className="text-gray-500 text-center py-4">No classes enrolled</p>
                    )}
                    {classes.length > 3 && (
                      <p className="text-sm text-gray-500 text-center">+{classes.length - 3} more classes</p>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Financial Information Card */}
            <div className="card mt-6">
              <div className="card__header">
                <h2 className="card__title">Financial Information</h2>
                <p className="card__subtitle">Monthly academy fees and payments</p>
              </div>
              <div className="card__content">
                {payments.slice(0, 3).map((payment, index) => (
                  <div key={index} className="card__payment">
                    <div>
                      <h4 className="font-medium">{payment.class_name || 'Academy Fee'}</h4>
                      <p className="text-sm text-gray-600">
                        {formatDate(payment.payment_date)}
                        {payment.payment_method && ` • ${payment.payment_method}`}
                      </p>
                    </div>
                    <div className="flex items-center">
                      <span className="font-medium mr-3">{formatCurrency(payment.amount)}</span>
                      <span className={`card__payment-status card__payment-status--${payment.status}`}>
                        {payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
                      </span>
                    </div>
                  </div>
                ))}
                {payments.length > 3 && (
                  <p className="text-sm text-gray-500 text-center mt-3">+{payments.length - 3} more payments</p>
                )}
                <div className="flex justify-between pt-4 border-t mt-4">
                  <span className="text-sm font-medium">Account Balance:</span>
                  <span className="text-sm font-bold">{formatCurrency(profileData.balance)}</span>
                </div>
                {totalDue > 0 && (
                  <div className="flex justify-between pt-2">
                    <span className="text-sm font-medium">Total Due:</span>
                    <span className="text-sm font-bold text-red-600">{formatCurrency(totalDue)}</span>
                  </div>
                )}
              </div>
              <div className="card__footer">
                <button className="w-full bg-orange-500 hover:bg-orange-600 text-white py-2 px-4 rounded-lg font-medium transition-colors">
                  Make Payment
                </button>
              </div>
            </div>

            {/* Recent Attendance Details */}
            {attendances.length > 0 && (
              <div className="card mt-6">
                <div className="card__header">
                  <h2 className="card__title">Recent Attendance</h2>
                  <p className="card__subtitle">Last {Math.min(attendances.length, 5)} sessions</p>
                </div>
                <div className="card__content">
                  <div className="space-y-3">
                    {attendances.slice(0, 5).map((attendance, index) => (
                      <div key={index} className="attendance-item">
                        <div className="attendance-item__info">
                          <h4>{attendance.class_name}</h4>
                          <p>{formatDate(attendance.date)}</p>
                        </div>
                        <div className={`attendance-item__status ${
                          attendance.attended
                            ? 'attendance-item__status--present'
                            : 'attendance-item__status--absent'
                        }`}>
                          {attendance.attended ? (
                            <>
                              <CheckCircle size={16} />
                              Present
                            </>
                          ) : (
                            <>
                              <XCircle size={16} />
                              Absent
                            </>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

          </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default PlayerProfile;
