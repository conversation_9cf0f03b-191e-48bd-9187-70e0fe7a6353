export const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api',

  TIMEOUT: 10000,

  HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  }
};

export const ENV_CONFIG = {
  development: {
    BASE_URL: 'http://localhost:8000/api',
    DEBUG: true,
  },
  production: {
    BASE_URL: 'https://your-production-domain.com/api',
    DEBUG: false,
  }
};

export const getCurrentConfig = () => {
  const env = import.meta.env.MODE || 'development';
  return ENV_CONFIG[env] || ENV_CONFIG.development;
};
