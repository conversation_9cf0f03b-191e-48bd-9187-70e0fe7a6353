// API Configuration
export const API_CONFIG = {
  // Change this to your Laravel backend URL
  BASE_URL: import.meta.env.VITE_API_BASE_URL || 'Login error: AxiosError {message: 'Request failed with status code 422', name: 'AxiosError', code: 'ERR_BAD_REQUEST', config: {…}, request: XMLHttpRequest, …}code: "ERR_BAD_REQUEST"config: {transitional: {…}, adapter: Array(3), transformRequest: Array(1), transformResponse: Array(1), timeout: 10000, …}message: "Request failed with status code 422"name: "AxiosError"request: XMLHttpRequest {onreadystatechange: null, readyState: 4, timeout: 10000, withCredentials: false, upload: XMLHttpRequestUpload, …}response: {data: {…}, status: 422, statusText: '', headers: AxiosHeaders, config: {…}, …}status: 422stack: "AxiosError: Request failed with status code 422\n    at settle (http://localhost:3000/node_modules/.vite/deps/axios.js?v=b60a84de:1229:12)\n    at XMLHttpRequest.onloadend (http://localhost:3000/node_modules/.vite/deps/axios.js?v=b60a84de:1561:7)\n    at Axios.request (http://localhost:3000/node_modules/.vite/deps/axios.js?v=b60a84de:2119:41)\n    at async Object.login (http://localhost:3000/src/services/api.js:44:22)\n    at async login (http://localhost:3000/src/context/AuthContext.jsx:46:24)\n    at async handleLogin (http://localhost:3000/src/components/authh/login.jsx:63:22)"[[Prototype]]: Error',
  
  // Request timeout in milliseconds
  TIMEOUT: 10000,
  
  // Default headers
  HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  }
};

// Environment-specific configurations
export const ENV_CONFIG = {
  development: {
    BASE_URL: 'http://localhost:8000/api',
    DEBUG: true,
  },
  production: {
    BASE_URL: 'https://your-production-domain.com/api',
    DEBUG: false,
  }
};

// Get current environment config
export const getCurrentConfig = () => {
  const env = import.meta.env.MODE || 'development';
  return ENV_CONFIG[env] || ENV_CONFIG.development;
};
