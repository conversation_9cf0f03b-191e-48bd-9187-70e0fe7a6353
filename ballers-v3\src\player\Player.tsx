import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { studentAPI } from '../services/api';
import { calculateAttendanceStats, formatCurrency, formatDate } from '../utils/helpers';
import toast from 'react-hot-toast';
// Correct single import statement
import {
  Button,
  Container,
  Card,
  CardHeader,
  CardContent,
  CardFooter
} from './components';

import {
  Image as ImageIcon,
  Trophy,
  Target,
  Medal,
  Star,
  FileText,
  Video,
  Bell,
  TrendingUp,
  Camera,
  CalendarDays,
  LogOut,
  BookOpen,
  DollarSign,
  // Activity,
  // Flame,
} from 'lucide-react';



// Your component code here...

import Navbar from '../components/NavBar/Navbar';
import Footer from '../components/Footer/Footer';

import './styles/global.css';

function App() {
  const { user, logout } = useAuth();
  const [profileData, setProfileData] = useState(null);
  const [classes, setClasses] = useState([]);
  const [attendances, setAttendances] = useState([]);
  const [payments, setPayments] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAllData();
  }, []);

  const fetchAllData = async () => {
    try {
      setLoading(true);
      const [profileRes, classesRes, attendancesRes, paymentsRes] = await Promise.all([
        studentAPI.getProfile(),
        studentAPI.getClasses(),
        studentAPI.getAttendances(),
        studentAPI.getPayments()
      ]);

      setProfileData(profileRes.student);
      setClasses(classesRes.classes || []);
      setAttendances(attendancesRes.attendances || []);
      setPayments(paymentsRes.payments || []);
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Failed to load profile data');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      toast.success('Logged out successfully');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // Calculate attendance stats per class
  const getAttendanceByClass = () => {
    const attendanceByClass = {};

    attendances.forEach(attendance => {
      const className = attendance.class_name;
      if (!attendanceByClass[className]) {
        attendanceByClass[className] = {
          attended: 0,
          missed: 0,
          total: 0,
          rate: 0
        };
      }

      attendanceByClass[className].total++;
      if (attendance.attended) {
        attendanceByClass[className].attended++;
      } else {
        attendanceByClass[className].missed++;
      }

      // Calculate rate
      attendanceByClass[className].rate = Math.round(
        (attendanceByClass[className].attended / attendanceByClass[className].total) * 100
      );
    });

    return attendanceByClass;
  };

  const attendanceByClass = getAttendanceByClass();

  // Calculate overall attendance stats for the main attendance card
  const overallAttendance = calculateAttendanceStats(attendances);

  // Map profile data to match original structure
  const playerInfo = profileData ? {
    name: profileData.english_name || 'Student',
    number: "#" + (profileData.id || '00'), // Using ID as number since backend doesn't have player number
    position: profileData.position || 'Player',
    team: "Ballers Academy",
    joinDate: profileData.registration_date ? `Joined ${formatDate(profileData.registration_date)}` : 'Recently joined',
    badges: 0, // Backend doesn't have badges, keeping as 0
    personalInfo: {
      age: profileData.age || 'N/A',
      height: profileData.height || 'N/A',
      weight: profileData.weight || 'N/A',
      level: profileData.level_of_player || 'N/A',
      gender: profileData.gender || 'N/A',
      school: profileData.school_name || 'N/A'
    }
  } : null;

  const achievements = [
    { id: 1, title: "Regional Championship", date: "Apr 2024", icon: Trophy },
    { id: 2, title: "Holiday Classic", date: "Dec 2023", icon: Medal }
  ];

  const announcements = [
    {
      id: 1,
      title: "Tournament Schedule Update",
      date: "Apr 15, 2024",
      content: "The Regional Championship schedule has been updated. Please check your email for details."
    },
    {
      id: 2,
      title: "Training Session Change",
      date: "Apr 10, 2024",
      content: "Saturday's training session will be held at the main court instead of the practice facility."
    }
  ];

  const coachMedia = [
    {
      id: 1,
      type: "image",
      url: "https://images.pexels.com/photos/3755440/pexels-photo-3755440.jpeg",
      title: "Jump Shot Form",
      date: "Apr 12, 2024"
    },
    {
      id: 2,
      type: "image",
      url: "https://images.pexels.com/photos/3755442/pexels-photo-3755442.jpeg",
      title: "Defense Positioning",
      date: "Apr 10, 2024"
    }
  ];

  const resources = [
    {
      id: 1,
      type: "pdf",
      title: "Shooting Mechanics Guide",
      size: "2.4 MB",
      date: "Apr 5, 2024"
    },
    {
      id: 2,
      type: "video",
      title: "Advanced Dribbling Techniques",
      duration: "15:30",
      date: "Apr 3, 2024"
    }
  ];

  const performanceStats = {
    shooting: {
      title: "Shooting Accuracy",
      value: "68%",
      trend: "+5%"
    },
    defense: {
      title: "Defensive Rating",
      value: "85",
      trend: "+3"
    },
    stamina: {
      title: "Stamina Level",
      value: "92%",
      trend: "+8%"
    }
  };

  // Loading state
  if (loading) {
    return (
      <div>
        <Navbar />
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading profile...</p>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  // Error state
  if (!profileData || !playerInfo) {
    return (
      <div>
        <Navbar />
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <p className="text-red-600">Failed to load profile data</p>
            <Button onClick={fetchAllData} className="mt-4">
              Retry
            </Button>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div>
      <Navbar />
      <div className="min-h-screen bg-neutral-50">
        <div className="profile-cover">
          <img
            src="https://images.pexels.com/photos/1752757/pexels-photo-1752757.jpeg"
            alt="Cover"
            className="profile-cover__image"
          />
          <Button className="profile-cover__change-btn">
            <ImageIcon size={18} className="mr-2" />
            Change Cover
          </Button>
          <Button
            onClick={handleLogout}
            className="absolute top-4 right-4 bg-red-500 hover:bg-red-600 text-white"
          >
            <LogOut size={18} className="mr-2" />
            Logout
          </Button>
        </div>

        <Container>
          <div className="profile-header">
            <div className="profile-header__main">
              <div className="profile-avatar">
                <img
                  src={profileData?.profile_photo || "https://images.pexels.com/photos/2834917/pexels-photo-2834917.jpeg"}
                  alt={playerInfo.name}
                  className="profile-avatar__image"
                />
                <Button className="profile-avatar__change-btn">
                  <ImageIcon size={14} />
                </Button>
              </div>
              <div className="profile-info">
                <div className="flex items-center gap-3 flex-wrap">
                  <h1 className="profile-info__name">{playerInfo.name}</h1>
                  <span className="profile-info__number">{playerInfo.number}</span>
                  <div className="profile-badges">
                    <Star size={16} className="text-yellow-500" />
                    <span>{playerInfo.badges} Badges</span>
                  </div>
                </div>
                <div className="profile-info__meta">
                  <Target size={14} className="text-primary-500"
                    // fill="#ed8936"
                    strokeWidth={1.5}
                  />
                  <span>{playerInfo.position}</span>
                  <Trophy size={14} className="text-primary-500" />
                  <span>{playerInfo.team}</span>
                  <CalendarDays size={14} className="text-primary-500" />
                  <span> {playerInfo.joinDate}</span>
                </div>
              </div>
            </div>
          </div>

          {/* #6B4EE6 */}

          <div className="profile-content">
            {/* <Card className="mb-6">
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Bell className="text-primary-600" size={24} />
                  <h2 className="card__title">Announcements</h2>
                </div>
              </CardHeader>
              <CardContent>
                <div className="announcements">
                  {announcements.map(announcement => (
                    <div key={announcement.id} className="announcement-item">
                      <div className="announcement-item__header">
                        <h3 className="announcement-item__title">{announcement.title}</h3>
                        <span className="announcement-item__date">{announcement.date}</span>
                      </div>
                      <p className="announcement-item__content">{announcement.content}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card> */}



            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardHeader>
                  <h2 className="card__title">Personal Information</h2>
                </CardHeader>
                <CardContent>
                  <div className="personal-info">
                    {Object.entries(playerInfo.personalInfo).map(([key, value]) => (
                      <div key={key} className="personal-info__item">
                        <span className="personal-info__label">{key.charAt(0).toUpperCase() + key.slice(1)}</span>
                        <span className="personal-info__value">{value}</span>
                      </div>
                    ))}
                    {/* Additional backend fields */}
                    {profileData?.email && (
                      <div className="personal-info__item">
                        <span className="personal-info__label">Email</span>
                        <span className="personal-info__value">{profileData.email}</span>
                      </div>
                    )}
                    {profileData?.parent_number && (
                      <div className="personal-info__item">
                        <span className="personal-info__label">Parent Contact</span>
                        <span className="personal-info__value">{profileData.parent_number}</span>
                      </div>
                    )}
                    {profileData?.category && (
                      <div className="personal-info__item">
                        <span className="personal-info__label">Category</span>
                        <span className="personal-info__value">{profileData.category}</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <h2 className="card__title">Attendance</h2>
                  <p className="card__subtitle">Current season attendance record by class</p>
                </CardHeader>
                <CardContent>
                  {Object.keys(attendanceByClass).length > 0 ? (
                    <div className="space-y-4">
                      {Object.entries(attendanceByClass).map(([className, stats]) => (
                        <div key={className} className="border-b pb-4 last:border-b-0">
                          <h3 className="font-semibold text-sm mb-2">{className}</h3>

                          {/* Progress Bar */}
                          <div className="card__progress mb-2">
                            <div
                              className="card__progress-bar"
                              style={{ width: `${stats.rate}%` }}
                            ></div>
                          </div>

                          {/* Stats */}
                          <div className="flex justify-between">
                            <div className="card__stat">
                              <span className="card__stat-value text-green-600">{stats.attended}</span>
                              <span className="card__stat-label">Attended</span>
                            </div>
                            <div className="card__stat">
                              <span className="card__stat-value text-red-600">{stats.missed}</span>
                              <span className="card__stat-label">Missed</span>
                            </div>
                            <div className="card__stat">
                              <span className="card__stat-value">{stats.total}</span>
                              <span className="card__stat-label">Total</span>
                            </div>
                            
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-4 text-gray-500">
                      <p>No attendance records found</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <h2 className="card__title">Achievements</h2>
                </CardHeader>
                <CardContent>
                  <div className="achievements">
                    {achievements.map(achievement => (
                      <div key={achievement.id} className="achievement-item">
                        <achievement.icon size={24} className="achievement-item__icon" />
                        <div>
                          <h3 className="achievement-item__title">{achievement.title}</h3>
                          <span className="achievement-item__date">{achievement.date}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* <Card className="mt-6">
              <CardHeader>
                <div className="flex items-center gap-2">
                  <TrendingUp className="text-primary-600" size={24} />
                  <h2 className="card__title">Performance Stats</h2>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {Object.entries(performanceStats).map(([key, stat]) => (
                    <div key={key} className="performance-stat">
                      <h3 className="performance-stat__title">{stat.title}</h3>
                      <div className="performance-stat__values">
                        <span className="performance-stat__main">{stat.value}</span>
                        <span className="performance-stat__trend performance-stat__trend--up">
                          {stat.trend}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card> */}
             {/* Enrolled Classes Section - New from Backend */}
            {classes.length > 0 && (
              <Card className="mb-6">
                <CardHeader>
                  <div className="flex items-center gap-2">
                    <BookOpen className="text-primary-600" size={24} />
                    <h2 className="card__title">Enrolled Classes</h2>
                  </div>
                  <p className="card__subtitle">Your current class schedule</p>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {classes.map((classItem, index) => (
                      <div key={index} className="class-item border rounded-lg p-4">
                        <h3 className="font-semibold text-lg">{classItem.name}</h3>
                        {classItem.instructor_name && (
                          <p className="text-sm text-gray-600 mt-1">Instructor: {classItem.instructor_name}</p>
                        )}
                        <p className="text-sm text-gray-700 mt-2">{classItem.schedule}</p>
                        {classItem.description && (
                          <p className="text-sm text-gray-600 mt-2">{classItem.description}</p>
                        )}
                        <div className="flex justify-between items-center mt-3">
                          <span className="text-primary-600 font-medium">{formatCurrency(classItem.price_per_month)}/month</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Recent Attendance Details - New from Backend */}
            {attendances.length > 0 && (
              <Card className="mt-6">
                <CardHeader>
                  <div className="flex items-center gap-2">
                    <CalendarDays className="text-primary-600" size={24} />
                    <h2 className="card__title">Recent Attendance</h2>
                  </div>
                  <p className="card__subtitle">Last {Math.min(attendances.length, 10)} training sessions</p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {attendances.slice(0, 10).map((attendance, index) => (
                      <div key={index} className="flex justify-between items-center p-3 border rounded-lg">
                        <div>
                          <h4 className="font-medium">{attendance.class_name}</h4>
                          <p className="text-sm text-gray-600">{formatDate(attendance.date)}</p>
                        </div>
                        <div className={`flex items-center gap-2 ${
                          attendance.attended ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {attendance.attended ? (
                            <>
                              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                              <span className="text-sm font-medium">Present</span>
                            </>
                          ) : (
                            <>
                              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                              <span className="text-sm font-medium">Absent</span>
                            </>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* <Card className="mt-6">
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Camera className="text-primary-600" size={24} />
                  <h2 className="card__title">Coach Media</h2>
                </div>
              </CardHeader>
              <CardContent>
                <div className="media-grid">
                  {coachMedia.map(media => (
                    <div key={media.id} className="media-item">
                      <img src={media.url} alt={media.title} className="media-item__image" />
                      <div className="media-item__overlay">
                        <h3 className="media-item__title">{media.title}</h3>
                        <span className="media-item__date">{media.date}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card> */}

            {/* <Card className="mt-6">
              <CardHeader>
                <div className="flex items-center gap-2">
                  <FileText className="text-primary-600" size={24} />
                  <h2 className="card__title">Resources</h2>
                </div>
              </CardHeader>
              <CardContent>
                <div className="resources">
                  {resources.map(resource => (
                    <div key={resource.id} className="resource-item">
                      {resource.type === 'pdf' ? (
                        <FileText size={24} className="resource-item__icon" />
                      ) : (
                        <Video size={24} className="resource-item__icon" />
                      )}
                      <div className="resource-item__info">
                        <h3 className="resource-item__title">{resource.title}</h3>
                        <span className="resource-item__meta">
                          {resource.type === 'pdf' ? resource.size : resource.duration}
                          <span className="mx-2">•</span>
                          {resource.date}
                        </span>
                      </div>
                      <Button variant="outline" size="sm">Download</Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card> */}

            <Card className="mt-6">
              <CardHeader>
                <div className="flex items-center gap-2">
                  <DollarSign className="text-primary-600" size={24} />
                  <h2 className="card__title">Financial Information</h2>
                </div>
                <p className="card__subtitle">Academy fees and payment history</p>
              </CardHeader>
              <CardContent>
                {payments && payments.length > 0 ? (
                  payments.slice(0, 5).map((payment, index) => (
                    <div key={index} className="card__payment">
                      <div>
                        <h4 className="font-medium">{payment?.class_name || 'Academy Fee'}</h4>
                        <p className="text-sm text-neutral-600">
                          {payment?.payment_date ? formatDate(payment.payment_date) : 'No date'}
                          {payment?.payment_method && ` • ${payment.payment_method}`}
                        </p>
                      </div>
                      <div className="flex items-center">
                        <span className="font-medium mr-3">{formatCurrency(payment?.amount || 0)}</span>
                        <span className={`card__payment-status card__payment-status--${payment?.status || 'pending'}`}>
                          {payment?.status ? payment.status.charAt(0).toUpperCase() + payment.status.slice(1) : 'Pending'}
                        </span>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-gray-500 text-center py-4">No payment records found</p>
                )}
                {profileData?.balance !== undefined && (
                  <div className="flex justify-between pt-4 border-t mt-4">
                    <span className="text-sm font-medium">Account Balance:</span>
                    <span className="text-sm font-bold">{formatCurrency(profileData.balance)}</span>
                  </div>
                )}
              </CardContent>
              <CardFooter>
                <Button className="w-full">Make Payment</Button>
              </CardFooter>
            </Card>
          </div>
        </Container>
      </div>
      <Footer />
    </div>

  );
}

export default App;
